@import "tailwindcss";

/* Import Moderat fonts */
@font-face {
  font-family: 'Moderat';
  src: url('/fonts/Moderat-Regular.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Moderat';
  src: url('/fonts/Moderat-Medium.woff2') format('woff2');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Moderat';
  src: url('/fonts/Moderat-Bold.woff2') format('woff2');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Moderat';
  src: url('/fonts/Moderat-Black.woff2') format('woff2');
  font-weight: 900;
  font-style: normal;
  font-display: swap;
}

@theme {
  --font-moderat: 'Moderat', system-ui, sans-serif;
  --color-background: #1a1a1a;
  --color-foreground: #f0f0f0;
  --color-muted: #a0a0a0;
  --color-accent: #f5f5f5;
  --color-accent-light: #2a2a2a;
  --color-border: #333333;
  --color-artistic-red: #e55a4a;
  --color-artistic-blue: #6b8bc7;
  --color-artistic-yellow: #f4c842;
}

:root {
  /* Sophisticated dark palette for artistic portfolio */
  --background: #1a1a1a;
  --foreground: #f0f0f0;
  --muted: #a0a0a0;
  --accent: #f5f5f5;
  --accent-light: #2a2a2a;
  --border: #333333;
  --artistic-red: #e55a4a;
  --artistic-blue: #6b8bc7;
  --artistic-yellow: #f4c842;
}

@media (prefers-color-scheme: light) {
  :root {
    --background: #f8f8f8;
    --foreground: #2a2a2a;
    --muted: #666666;
    --accent: #2a2a2a;
    --accent-light: #f0f0f0;
    --border: #e0e0e0;
    --artistic-red: #c44536;
    --artistic-blue: #4a6fa5;
    --artistic-yellow: #d4af37;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: 'Moderat', system-ui, -apple-system, sans-serif;
  font-feature-settings: "kern" 1, "liga" 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  line-height: 1.6;
}

/* Artistic typography styles */
.artistic-heading {
  font-weight: 500;
  letter-spacing: -0.02em;
  line-height: 1.1;
}

.artistic-body {
  font-weight: 400;
  letter-spacing: -0.01em;
  line-height: 1.7;
}

.artistic-accent {
  color: var(--artistic-red);
}

/* Experimental layout utilities */
.asymmetric-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
  align-items: start;
}

.artistic-border {
  border: 1px solid var(--border);
}

/* Smooth transitions for artistic interactions */
* {
  transition: color 0.2s ease, background-color 0.2s ease, border-color 0.2s ease;
}

/* Custom cursor only when spatial canvas is active */
.spatial-canvas {
  cursor: none;
}

/* Spatial layout utilities */
.spatial-element {
  position: absolute;
  transform-origin: center;
}

.spatial-element:hover {
  z-index: 10;
}

/* Minimal scrollbar for overlays */
::-webkit-scrollbar {
  width: 2px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: var(--muted);
  border-radius: 1px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--foreground);
}

/* Focus styles for accessibility */
button:focus-visible,
[tabindex]:focus-visible {
  outline: 1px solid var(--artistic-red);
  outline-offset: 2px;
}

/* Artistic hover states */
.artistic-hover:hover {
  transform: translateY(-2px);
  transition: transform 0.2s ease;
}

/* Text selection styling */
::selection {
  background: var(--artistic-red);
  color: var(--background);
}
