"use client";
import { motion } from "framer-motion";
import Link from "next/link";

const cvData = {
  education: [
    {
      institution: "Tehran University of Art",
      degree: "Master of Fine Arts",
      field: "Visual Communication Design",
      year: "2022-2024",
      location: "Tehran, Iran"
    },
    {
      institution: "Azad University",
      degree: "Bachelor of Arts",
      field: "Graphic Design",
      year: "2018-2022",
      location: "Tehran, Iran"
    }
  ],
  experience: [
    {
      role: "Visual Researcher",
      organization: "Herfeh:Honarmand Cultural Institute",
      year: "2024-Present",
      location: "Tehran, Iran",
      description: "Contemporary Persian visual language research and editorial design"
    },
    {
      role: "Type Designer",
      organization: "Independent Practice",
      year: "2023-Present",
      location: "Tehran, Iran",
      description: "Experimental typography for contemporary Persian poetry collections"
    },
    {
      role: "Multimedia Artist",
      organization: "Ettefagh Collective",
      year: "2022-2023",
      location: "Tehran, Iran",
      description: "Cross-disciplinary sound and vision projects"
    },
    {
      role: "Visual Anthropologist",
      organization: "Tehran Urban Studies",
      year: "2023",
      location: "Tehran, Iran",
      description: "Urban archaeology through visual documentation and analysis"
    }
  ],
  exhibitions: [
    {
      title: "Digital Calligraphy: Code as Script",
      venue: "Tehran Contemporary Art Gallery",
      year: "2024",
      type: "Solo Exhibition"
    },
    {
      title: "Persian Typography Today",
      venue: "Cultural Center of Iran",
      year: "2023",
      type: "Group Exhibition"
    },
    {
      title: "Urban Fragments",
      venue: "Azad Art Gallery",
      year: "2023",
      type: "Solo Exhibition"
    }
  ],
  awards: [
    {
      title: "Excellence in Visual Research",
      organization: "Iranian Graphic Design Association",
      year: "2024"
    },
    {
      title: "Best Typography Project",
      organization: "Tehran Design Week",
      year: "2023"
    }
  ]
};

export default function CVPage() {
  return (
    <div className="min-h-screen bg-background">
      {/* Back navigation */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="fixed top-8 left-8 z-50"
      >
        <Link
          href="/"
          className="group flex items-center space-x-2 text-muted hover:text-foreground transition-colors"
        >
          <span className="text-lg group-hover:-translate-x-1 transition-transform">←</span>
          <span className="text-sm uppercase tracking-wider">Back</span>
        </Link>
      </motion.div>

      {/* Main content */}
      <div className="max-w-4xl mx-auto px-8 py-20">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="mb-20"
        >
          <h1 className="artistic-heading text-4xl md:text-5xl font-medium mb-4">
            Mehdi Asadi
          </h1>
          <div className="h-px bg-artistic-red w-16 mb-6" />
          <p className="text-lg text-muted max-w-2xl">
            Visual Artist & Researcher — Tehran, Iran
          </p>
        </motion.div>

        {/* Education */}
        <motion.section
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          className="mb-16"
        >
          <h2 className="text-2xl font-medium mb-8 text-artistic-blue">Education</h2>
          <div className="space-y-6">
            {cvData.education.map((edu, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.3 + index * 0.1 }}
                className="border-l-2 border-border pl-6 pb-6"
              >
                <div className="flex flex-col md:flex-row md:justify-between md:items-start mb-2">
                  <h3 className="text-lg font-medium">{edu.degree}</h3>
                  <span className="text-sm text-muted">{edu.year}</span>
                </div>
                <p className="text-muted mb-1">{edu.field}</p>
                <p className="text-sm text-muted">{edu.institution} — {edu.location}</p>
              </motion.div>
            ))}
          </div>
        </motion.section>

        {/* Experience */}
        <motion.section
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          className="mb-16"
        >
          <h2 className="text-2xl font-medium mb-8 text-artistic-red">Experience</h2>
          <div className="space-y-8">
            {cvData.experience.map((exp, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.5 + index * 0.1 }}
                className="border-l-2 border-border pl-6 pb-6"
              >
                <div className="flex flex-col md:flex-row md:justify-between md:items-start mb-2">
                  <h3 className="text-lg font-medium">{exp.role}</h3>
                  <span className="text-sm text-muted">{exp.year}</span>
                </div>
                <p className="text-muted mb-2">{exp.organization} — {exp.location}</p>
                <p className="text-sm text-muted leading-relaxed">{exp.description}</p>
              </motion.div>
            ))}
          </div>
        </motion.section>

        {/* Exhibitions */}
        <motion.section
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          className="mb-16"
        >
          <h2 className="text-2xl font-medium mb-8 text-artistic-yellow">Exhibitions</h2>
          <div className="space-y-6">
            {cvData.exhibitions.map((exhibition, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.7 + index * 0.1 }}
                className="border-l-2 border-border pl-6 pb-6"
              >
                <div className="flex flex-col md:flex-row md:justify-between md:items-start mb-2">
                  <h3 className="text-lg font-medium">{exhibition.title}</h3>
                  <span className="text-sm text-muted">{exhibition.year}</span>
                </div>
                <p className="text-muted mb-1">{exhibition.venue}</p>
                <p className="text-sm text-muted">{exhibition.type}</p>
              </motion.div>
            ))}
          </div>
        </motion.section>

        {/* Awards */}
        <motion.section
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.8 }}
          className="mb-16"
        >
          <h2 className="text-2xl font-medium mb-8 text-artistic-blue">Recognition</h2>
          <div className="space-y-6">
            {cvData.awards.map((award, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.9 + index * 0.1 }}
                className="border-l-2 border-border pl-6 pb-6"
              >
                <div className="flex flex-col md:flex-row md:justify-between md:items-start mb-2">
                  <h3 className="text-lg font-medium">{award.title}</h3>
                  <span className="text-sm text-muted">{award.year}</span>
                </div>
                <p className="text-muted">{award.organization}</p>
              </motion.div>
            ))}
          </div>
        </motion.section>
      </div>
    </div>
  );
}
